<template>
  <div class="group-create-steps">
    <el-steps :active="currentStep" align-center class="create-steps">
      <el-step title="基础信息" description="群组基本信息"></el-step>
      <el-step title="营销设置" description="营销和展示配置"></el-step>
      <el-step title="内容配置" description="内容和模板设置"></el-step>
      <el-step title="完成创建" description="确认并创建群组"></el-step>
    </el-steps>

    <div class="step-content">
      <!-- 步骤1：基础信息 -->
      <div v-show="currentStep === 0" class="step-panel">
        <h3 class="step-title">
          <el-icon><InfoFilled /></el-icon>
          基础信息
        </h3>
        <el-divider />
        
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="群组名称" prop="title">
                <el-input 
                  v-model="form.title" 
                  placeholder="请输入群组名称，支持xxx占位符"
                  maxlength="200"
                  show-word-limit
                />
                <div class="form-tip">
                  💡 使用"xxx"作为占位符，系统会自动替换为用户所在城市
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="群组价格" prop="price">
                <el-input-number
                  v-model="form.price"
                  :min="0"
                  :precision="2"
                  placeholder="0.00"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="群组类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择群组类型" style="width: 300px">
              <el-option label="社区群" value="community" />
              <el-option label="VIP群" value="vip" />
              <el-option label="学习群" value="education" />
              <el-option label="商务群" value="business" />
            </el-select>
          </el-form-item>

          <el-form-item label="群组描述" prop="description">
            <el-input 
              v-model="form.description" 
              type="textarea" 
              :rows="4"
              placeholder="请输入群组描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤2：营销设置 -->
      <div v-show="currentStep === 1" class="step-panel">
        <h3 class="step-title">
          <el-icon><Promotion /></el-icon>
          营销设置
        </h3>
        <el-divider />

        <el-form :model="form" label-width="120px">
          <!-- 付费后内容配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Document /></el-icon>
                <span>付费后内容</span>
              </div>
            </template>

            <el-form-item label="内容类型">
              <el-radio-group v-model="form.paid_content_type">
                <el-radio value="qr_code">入群二维码</el-radio>
                <el-radio value="image">图片资源</el-radio>
                <el-radio value="link">下载链接</el-radio>
                <el-radio value="document">文档内容</el-radio>
                <el-radio value="video">视频内容</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 二维码配置 -->
            <div v-if="form.paid_content_type === 'qr_code'">
              <el-form-item label="入群二维码">
                <el-upload
                  class="qr-uploader"
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :show-file-list="false"
                  :on-success="handleQRSuccess"
                  :before-upload="beforeUpload"
                >
                  <img v-if="form.qr_code" :src="form.qr_code" class="qr-image">
                  <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </div>

            <!-- 链接配置 -->
            <div v-if="form.paid_content_type === 'link'">
              <el-form-item label="下载链接">
                <el-input v-model="form.paid_link" placeholder="请输入下载链接" />
              </el-form-item>
              <el-form-item label="链接描述">
                <el-input v-model="form.paid_link_desc" placeholder="请输入链接描述" />
              </el-form-item>
            </div>

            <!-- 文档内容配置 -->
            <div v-if="form.paid_content_type === 'document'">
              <el-form-item label="文档内容">
                <el-input
                  v-model="form.paid_document_content"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入文档内容"
                />
              </el-form-item>
            </div>
          </el-card>

          <!-- 城市定位配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Location /></el-icon>
                <span>城市定位</span>
                <el-switch
                  v-model="form.auto_city_replace"
                  class="header-switch"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="关闭"
                />
              </div>
            </template>

            <div v-if="form.auto_city_replace">
              <el-form-item label="插入策略">
                <el-select v-model="form.city_insert_strategy" placeholder="选择策略" style="width: 200px">
                  <el-option label="自动替换xxx" value="auto" />
                  <el-option label="前缀插入" value="prefix" />
                  <el-option label="后缀插入" value="suffix" />
                </el-select>
              </el-form-item>

              <el-form-item label="测试替换">
                <el-row :gutter="12">
                  <el-col :span="8">
                    <el-input v-model="testCity" placeholder="输入城市名" />
                  </el-col>
                  <el-col :span="8">
                    <el-button @click="testCityReplacement">测试</el-button>
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="testResult" placeholder="替换结果" readonly />
                  </el-col>
                </el-row>
              </el-form-item>
            </div>
          </el-card>

          <!-- 营销展示配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><DataAnalysis /></el-icon>
                <span>营销展示</span>
              </div>
            </template>

            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="阅读数显示">
                  <el-input v-model="form.read_count_display" placeholder="如：10万+" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="点赞数">
                  <el-input-number v-model="form.like_count" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="想看数">
                  <el-input-number v-model="form.want_see_count" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="入群按钮文案">
                  <el-input v-model="form.button_title" placeholder="如：立即加入群聊" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="头像库选择">
                  <el-select v-model="form.avatar_library" style="width: 100%">
                    <el-option label="QQ头像库" value="qq" />
                    <el-option label="微信头像库" value="wechat" />
                    <el-option label="随机头像库" value="random" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 虚拟数据配置 -->
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="虚拟成员数">
                  <el-input-number v-model="form.virtual_members" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="虚拟订单数">
                  <el-input-number v-model="form.virtual_orders" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="今日浏览量">
                  <el-input-number v-model="form.today_views" :min="0" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
        </el-form>
      </div>

      <!-- 步骤3：内容配置 -->
      <div v-show="currentStep === 2" class="step-panel">
        <h3 class="step-title">
          <el-icon><Document /></el-icon>
          内容配置
        </h3>
        <el-divider />

        <el-form :model="form" label-width="120px">
          <!-- 内容管理 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Document /></el-icon>
                <span>内容管理</span>
              </div>
            </template>

            <el-form-item label="群简介标题">
              <el-input v-model="form.group_intro_title" placeholder="群简介" />
            </el-form-item>

            <el-form-item label="群简介内容">
              <el-input
                v-model="form.group_intro_content"
                type="textarea"
                :rows="4"
                placeholder="请输入群简介内容"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="FAQ标题">
              <el-input v-model="form.faq_title" placeholder="常见问题" />
            </el-form-item>

            <el-form-item label="FAQ内容">
              <el-input
                v-model="form.faq_content"
                type="textarea"
                :rows="4"
                placeholder="请输入常见问题内容"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="群友评价">
              <el-input
                v-model="form.member_reviews"
                type="textarea"
                :rows="3"
                placeholder="请输入群友评价内容"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-card>

          <!-- 客服信息配置 -->
          <el-card class="config-section" shadow="never">
            <template #header>
              <div class="section-header">
                <el-icon><Service /></el-icon>
                <span>客服信息</span>
                <el-switch
                  v-model="form.show_customer_service"
                  class="header-switch"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="关闭"
                />
              </div>
            </template>

            <div v-if="form.show_customer_service">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="客服标题">
                    <el-input v-model="form.customer_service_title" placeholder="联系客服" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客服描述">
                    <el-input v-model="form.customer_service_desc" placeholder="有问题请联系客服" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="客服头像">
                    <el-upload
                      class="avatar-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeUpload"
                    >
                      <img v-if="form.customer_service_avatar" :src="form.customer_service_avatar" class="avatar">
                      <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="客服二维码">
                    <el-upload
                      class="qr-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handleServiceQRSuccess"
                      :before-upload="beforeUpload"
                    >
                      <img v-if="form.customer_service_qr" :src="form.customer_service_qr" class="qr-image">
                      <el-icon v-else class="qr-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-form>
      </div>

      <!-- 步骤4：完成创建 -->
      <div v-show="currentStep === 3" class="step-panel">
        <h3 class="step-title">
          <el-icon><Check /></el-icon>
          完成创建
        </h3>
        <el-divider />

        <div class="summary-content">
          <el-descriptions title="群组信息预览" :column="2" border>
            <el-descriptions-item label="群组名称">{{ form.title }}</el-descriptions-item>
            <el-descriptions-item label="群组价格">¥{{ form.price }}</el-descriptions-item>
            <el-descriptions-item label="群组类型">{{ getTypeLabel(form.type) }}</el-descriptions-item>
            <el-descriptions-item label="城市定位">{{ form.auto_city_replace ? '已启用' : '未启用' }}</el-descriptions-item>
            <el-descriptions-item label="阅读数">{{ form.read_count_display }}</el-descriptions-item>
            <el-descriptions-item label="点赞数">{{ form.like_count }}</el-descriptions-item>
            <el-descriptions-item label="入群按钮">{{ form.button_title }}</el-descriptions-item>
            <el-descriptions-item label="头像库">{{ getAvatarLibraryLabel(form.avatar_library) }}</el-descriptions-item>
          </el-descriptions>

          <div class="summary-description">
            <h4>群组描述：</h4>
            <p>{{ form.description || '暂无描述' }}</p>
          </div>

          <div class="summary-intro">
            <h4>群简介：</h4>
            <p>{{ form.group_intro_content || '暂无群简介' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤控制按钮 -->
    <div class="step-controls">
      <el-button v-if="currentStep > 0" @click="prevStep">上一步</el-button>
      <el-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</el-button>
      <el-button v-if="currentStep === 3" type="success" @click="handleSubmit" :loading="submitting">
        <el-icon><Check /></el-icon>
        创建群组
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  InfoFilled, Promotion, Location, DataAnalysis, Document, Check, Plus, Service
} from '@element-plus/icons-vue'
import { createGroup } from '@/api/community'

// Props定义
const props = defineProps({
  // 用户角色
  userRole: {
    type: String,
    default: 'owner'
  },
  // 默认值配置
  defaultValues: {
    type: Object,
    default: () => ({})
  }
})

// Emits定义
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const formRef = ref()
const currentStep = ref(0)
const submitting = ref(false)
const testCity = ref('北京')
const testResult = ref('')

// 表单数据（基于增强版群组创建的完整功能）
const form = reactive({
  // 基础信息
  title: '',
  price: 0,
  payment_methods: ['wechat', 'alipay'],
  type: 'community',
  status: 'active',
  description: '',

  // 付费后内容配置
  paid_content_type: 'qr_code',
  qr_code: '',
  paid_images: [],
  paid_link: '',
  paid_link_desc: '',
  paid_document_content: '',
  paid_video_url: '',
  paid_video_title: '',
  paid_video_desc: '',

  // 城市定位配置
  auto_city_replace: 1,
  city_insert_strategy: 'auto',

  // 营销展示配置
  read_count_display: '8万+',
  like_count: 1500,
  want_see_count: 1000,
  button_title: '加入学习群',
  avatar_library: 'qq',
  display_type: 1,
  wx_accessible: 1,

  // 内容配置
  group_intro_title: '群简介',
  group_intro_content: '',
  faq_title: '常见问题',
  faq_content: '',
  member_reviews: '',

  // 虚拟数据配置
  virtual_members: 100,
  virtual_orders: 50,
  virtual_income: 5000.00,
  today_views: 1200,
  show_virtual_activity: 1,
  show_member_avatars: 1,
  show_member_reviews: 1,

  // 客服配置
  show_customer_service: 1,
  customer_service_title: '',
  customer_service_desc: '',
  customer_service_avatar: '',
  customer_service_qr: '',
  ad_qr_code: '',

  ...props.defaultValues
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入群组名称', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入群组价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择群组类型', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入群组描述', trigger: 'blur' }
  ]
}

// 方法
const nextStep = async () => {
  if (currentStep.value === 0) {
    try {
      await formRef.value.validate()
      currentStep.value++
    } catch (error) {
      ElMessage.warning('请完善基础信息')
    }
  } else {
    currentStep.value++
  }
}

const prevStep = () => {
  currentStep.value--
}

const testCityReplacement = () => {
  if (!form.title || !testCity.value) {
    ElMessage.warning('请输入群组名称和测试城市')
    return
  }

  testResult.value = form.title.replace(/xxx/g, testCity.value)
  ElMessage.success('城市替换测试完成')
}

// 上传相关配置
const uploadUrl = computed(() => '/api/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}))

// 上传处理方法
const beforeUpload = (file) => {
  const isImage = file.type.indexOf('image/') === 0
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleQRSuccess = (response) => {
  form.qr_code = response.url
  ElMessage.success('二维码上传成功')
}

const handleAvatarSuccess = (response) => {
  form.customer_service_avatar = response.url
  ElMessage.success('头像上传成功')
}

const handleServiceQRSuccess = (response) => {
  form.customer_service_qr = response.url
  ElMessage.success('客服二维码上传成功')
}

const getTypeLabel = (type) => {
  const typeMap = {
    community: '社区群',
    vip: 'VIP群',
    education: '学习群',
    business: '商务群'
  }
  return typeMap[type] || type
}

const getAvatarLibraryLabel = (library) => {
  const libraryMap = {
    qq: 'QQ头像库',
    wechat: '微信头像库',
    random: '随机头像库'
  }
  return libraryMap[library] || library
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 构建提交数据
    const submitData = {
      ...form,
      user_role: props.userRole
    }
    
    // 调用API创建群组
    const response = await createGroup(submitData)
    
    if (response.code === 200) {
      ElMessage.success('群组创建成功！')
      emit('success', response.data)
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建群组失败:', error)
    ElMessage.error('创建失败，请重试')
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.group-create-steps {
  .create-steps {
    margin-bottom: 30px;
  }
  
  .step-content {
    min-height: 400px;
  }
  
  .step-panel {
    .step-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
    
    .config-section {
      margin-bottom: 20px;
      
      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        
        .header-switch {
          margin-left: auto;
        }
      }
    }
    
    .form-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }
    
    .summary-content {
      .summary-description,
      .summary-intro {
        margin-top: 20px;
        
        h4 {
          margin: 0 0 8px 0;
          color: #1f2937;
        }
        
        p {
          margin: 0;
          color: #4b5563;
          line-height: 1.6;
        }
      }
    }
  }
  
  .step-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
  }

  /* 上传组件样式 */
  .qr-uploader, .avatar-uploader {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .qr-uploader .el-upload {
    width: 120px;
    height: 120px;
  }

  .avatar-uploader .el-upload {
    width: 80px;
    height: 80px;
  }

  .qr-image, .avatar {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }

  .qr-uploader-icon, .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .group-create-steps {
    .step-controls {
      flex-wrap: wrap;
      gap: 8px;
      
      .el-button {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}
</style>
